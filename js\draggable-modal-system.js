// Universal Draggable Modal System
// Supports all modals with drag, resize, minimize, maximize, and z-index management

class DraggableModalSystem {
    constructor() {
        this.modals = new Map();
        this.highestZIndex = 1000;
        this.activeModal = null;
        this.isDragging = false;
        this.isResizing = false;
        this.dragOffset = { x: 0, y: 0 };
        this.resizeHandle = null;
        
        // Initialize the system
        this.init();
    }

    init() {
        // Find all modals and make them draggable
        this.initializeAllModals();
        
        // Add global event listeners
        this.addGlobalEventListeners();
        
        // Add CSS for draggable functionality
        this.addDraggableStyles();
        
        console.log('Universal Draggable Modal System initialized');
    }

    initializeAllModals() {
        // List of all modal selectors in the project
        const modalSelectors = [
            '#editModal',
            '#transactionDetailsModal', 
            '#searchModal',
            '#imageModal',
            '#notificationDetailModal',
            '#colorPickerModal',
            '#noteEditorModal'
        ];

        modalSelectors.forEach(selector => {
            const modal = document.querySelector(selector);
            if (modal) {
                this.makeModalDraggable(modal);
            }
        });
    }

    makeModalDraggable(modal) {
        const modalId = modal.id;
        
        // Skip if already initialized
        if (this.modals.has(modalId)) {
            return;
        }

        // Find modal content
        const modalContent = modal.querySelector('.modal-content, .search-modal-content, .note-editor-content, .color-picker-content');
        if (!modalContent) {
            console.warn(`No modal content found for ${modalId}`);
            return;
        }

        // Create modal state object
        const modalState = {
            id: modalId,
            element: modal,
            content: modalContent,
            isMinimized: false,
            isMaximized: false,
            originalPosition: null,
            originalSize: null,
            zIndex: this.highestZIndex++
        };

        // Add draggable functionality
        this.addDragFunctionality(modalState);
        
        // Add resize functionality
        this.addResizeFunctionality(modalState);
        
        // Add window controls
        this.addWindowControls(modalState);
        
        // Add modal to registry
        this.modals.set(modalId, modalState);
        
        // Set initial z-index
        modal.style.zIndex = modalState.zIndex;
        
        console.log(`Modal ${modalId} made draggable`);
    }

    addDragFunctionality(modalState) {
        const { content } = modalState;
        
        // Find or create drag handle (modal header)
        let dragHandle = content.querySelector('.modal-header, .search-modal-header, .note-editor-header, .color-picker-header');
        
        if (!dragHandle) {
            // Create a drag handle if none exists
            dragHandle = document.createElement('div');
            dragHandle.className = 'modal-drag-handle';
            dragHandle.innerHTML = '<i class="fas fa-grip-horizontal"></i> Drag';
            content.insertBefore(dragHandle, content.firstChild);
        }

        // Add drag handle styling
        dragHandle.style.cursor = 'move';
        dragHandle.style.userSelect = 'none';
        dragHandle.setAttribute('data-drag-handle', 'true');

        // Add drag event listeners
        dragHandle.addEventListener('mousedown', (e) => this.startDrag(e, modalState));
        dragHandle.addEventListener('touchstart', (e) => this.startDrag(e, modalState), { passive: false });
    }

    addResizeFunctionality(modalState) {
        const { content } = modalState;
        
        // Add resize handles
        const resizeHandles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];
        
        resizeHandles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `resize-handle resize-${direction}`;
            handle.setAttribute('data-direction', direction);
            content.appendChild(handle);
            
            // Add resize event listeners
            handle.addEventListener('mousedown', (e) => this.startResize(e, modalState, direction));
            handle.addEventListener('touchstart', (e) => this.startResize(e, modalState, direction), { passive: false });
        });
    }

    addWindowControls(modalState) {
        const { content } = modalState;
        
        // Find existing controls container or create one
        let controlsContainer = content.querySelector('.modal-controls, .search-modal-controls, .note-editor-controls');
        
        if (!controlsContainer) {
            controlsContainer = document.createElement('div');
            controlsContainer.className = 'modal-window-controls';
            
            // Find header to append controls
            const header = content.querySelector('.modal-header, .search-modal-header, .note-editor-header, .color-picker-header');
            if (header) {
                header.appendChild(controlsContainer);
            } else {
                content.insertBefore(controlsContainer, content.firstChild);
            }
        }

        // Add minimize button
        const minimizeBtn = document.createElement('button');
        minimizeBtn.className = 'modal-control-btn modal-minimize';
        minimizeBtn.innerHTML = '<i class="fas fa-window-minimize"></i>';
        minimizeBtn.title = 'Minimize';
        minimizeBtn.addEventListener('click', () => this.minimizeModal(modalState));

        // Add maximize/restore button
        const maximizeBtn = document.createElement('button');
        maximizeBtn.className = 'modal-control-btn modal-maximize';
        maximizeBtn.innerHTML = '<i class="fas fa-window-maximize"></i>';
        maximizeBtn.title = 'Maximize';
        maximizeBtn.addEventListener('click', () => this.toggleMaximize(modalState));

        // Add to controls (if not already present)
        if (!controlsContainer.querySelector('.modal-minimize')) {
            controlsContainer.appendChild(minimizeBtn);
        }
        if (!controlsContainer.querySelector('.modal-maximize')) {
            controlsContainer.appendChild(maximizeBtn);
        }
    }

    startDrag(e, modalState) {
        e.preventDefault();
        
        // Don't drag if modal is maximized
        if (modalState.isMaximized) {
            return;
        }
        
        this.isDragging = true;
        this.activeModal = modalState;
        
        // Bring modal to front
        this.bringToFront(modalState);
        
        // Get initial mouse/touch position
        const clientX = e.touches ? e.touches[0].clientX : e.clientX;
        const clientY = e.touches ? e.touches[0].clientY : e.clientY;
        
        // Get modal content position
        const rect = modalState.content.getBoundingClientRect();
        this.dragOffset.x = clientX - rect.left;
        this.dragOffset.y = clientY - rect.top;
        
        // Add dragging class
        modalState.content.classList.add('dragging');
        
        // Prevent text selection
        document.body.style.userSelect = 'none';
    }

    startResize(e, modalState, direction) {
        e.preventDefault();
        e.stopPropagation();
        
        // Don't resize if modal is maximized
        if (modalState.isMaximized) {
            return;
        }
        
        this.isResizing = true;
        this.activeModal = modalState;
        this.resizeHandle = direction;
        
        // Bring modal to front
        this.bringToFront(modalState);
        
        // Store initial size and position
        const rect = modalState.content.getBoundingClientRect();
        this.resizeData = {
            startX: e.touches ? e.touches[0].clientX : e.clientX,
            startY: e.touches ? e.touches[0].clientY : e.clientY,
            startWidth: rect.width,
            startHeight: rect.height,
            startLeft: rect.left,
            startTop: rect.top
        };
        
        // Add resizing class
        modalState.content.classList.add('resizing');
        
        // Prevent text selection
        document.body.style.userSelect = 'none';
    }

    addGlobalEventListeners() {
        // Mouse events
        document.addEventListener('mousemove', (e) => this.handleMove(e));
        document.addEventListener('mouseup', () => this.stopDragResize());
        
        // Touch events
        document.addEventListener('touchmove', (e) => this.handleMove(e), { passive: false });
        document.addEventListener('touchend', () => this.stopDragResize());
        
        // Click to bring modal to front
        document.addEventListener('click', (e) => {
            const modal = e.target.closest('.modal, .search-modal, .note-editor-modal, .color-picker-modal');
            if (modal && this.modals.has(modal.id)) {
                this.bringToFront(this.modals.get(modal.id));
            }
        });
        
        // Escape key to close active modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal(this.activeModal);
            }
        });
    }

    handleMove(e) {
        if (!this.activeModal) return;
        
        e.preventDefault();
        
        const clientX = e.touches ? e.touches[0].clientX : e.clientX;
        const clientY = e.touches ? e.touches[0].clientY : e.clientY;
        
        if (this.isDragging) {
            this.handleDrag(clientX, clientY);
        } else if (this.isResizing) {
            this.handleResize(clientX, clientY);
        }
    }

    handleDrag(clientX, clientY) {
        const { content } = this.activeModal;
        
        // Calculate new position
        let newLeft = clientX - this.dragOffset.x;
        let newTop = clientY - this.dragOffset.y;
        
        // Keep modal within viewport bounds
        const maxLeft = window.innerWidth - content.offsetWidth;
        const maxTop = window.innerHeight - content.offsetHeight;
        
        newLeft = Math.max(0, Math.min(newLeft, maxLeft));
        newTop = Math.max(0, Math.min(newTop, maxTop));
        
        // Apply new position
        content.style.position = 'fixed';
        content.style.left = newLeft + 'px';
        content.style.top = newTop + 'px';
        content.style.margin = '0';
    }

    handleResize(clientX, clientY) {
        const { content } = this.activeModal;
        const { startX, startY, startWidth, startHeight, startLeft, startTop } = this.resizeData;
        
        const deltaX = clientX - startX;
        const deltaY = clientY - startY;
        
        let newWidth = startWidth;
        let newHeight = startHeight;
        let newLeft = startLeft;
        let newTop = startTop;
        
        // Calculate new dimensions based on resize direction
        switch (this.resizeHandle) {
            case 'se': // Southeast
                newWidth = startWidth + deltaX;
                newHeight = startHeight + deltaY;
                break;
            case 'sw': // Southwest
                newWidth = startWidth - deltaX;
                newHeight = startHeight + deltaY;
                newLeft = startLeft + deltaX;
                break;
            case 'ne': // Northeast
                newWidth = startWidth + deltaX;
                newHeight = startHeight - deltaY;
                newTop = startTop + deltaY;
                break;
            case 'nw': // Northwest
                newWidth = startWidth - deltaX;
                newHeight = startHeight - deltaY;
                newLeft = startLeft + deltaX;
                newTop = startTop + deltaY;
                break;
            case 'e': // East
                newWidth = startWidth + deltaX;
                break;
            case 'w': // West
                newWidth = startWidth - deltaX;
                newLeft = startLeft + deltaX;
                break;
            case 's': // South
                newHeight = startHeight + deltaY;
                break;
            case 'n': // North
                newHeight = startHeight - deltaY;
                newTop = startTop + deltaY;
                break;
        }
        
        // Apply minimum size constraints
        const minWidth = 300;
        const minHeight = 200;
        
        if (newWidth < minWidth) {
            newWidth = minWidth;
            if (this.resizeHandle.includes('w')) {
                newLeft = startLeft + startWidth - minWidth;
            }
        }
        
        if (newHeight < minHeight) {
            newHeight = minHeight;
            if (this.resizeHandle.includes('n')) {
                newTop = startTop + startHeight - minHeight;
            }
        }
        
        // Apply new size and position
        content.style.width = newWidth + 'px';
        content.style.height = newHeight + 'px';
        content.style.left = newLeft + 'px';
        content.style.top = newTop + 'px';
        content.style.position = 'fixed';
        content.style.margin = '0';
    }

    stopDragResize() {
        if (this.isDragging || this.isResizing) {
            // Remove dragging/resizing classes
            if (this.activeModal) {
                this.activeModal.content.classList.remove('dragging', 'resizing');
            }

            // Reset states
            this.isDragging = false;
            this.isResizing = false;
            this.resizeHandle = null;

            // Restore text selection
            document.body.style.userSelect = '';
        }
    }

    bringToFront(modalState) {
        modalState.zIndex = ++this.highestZIndex;
        modalState.element.style.zIndex = modalState.zIndex;
        this.activeModal = modalState;
    }

    minimizeModal(modalState) {
        const { element, content } = modalState;

        if (modalState.isMinimized) {
            // Restore modal
            content.style.display = '';
            modalState.isMinimized = false;

            // Update minimize button
            const minimizeBtn = content.querySelector('.modal-minimize i');
            if (minimizeBtn) {
                minimizeBtn.className = 'fas fa-window-minimize';
            }
        } else {
            // Minimize modal
            content.style.display = 'none';
            modalState.isMinimized = true;

            // Create minimized indicator
            this.createMinimizedIndicator(modalState);
        }
    }

    createMinimizedIndicator(modalState) {
        // Remove existing indicator
        const existingIndicator = document.querySelector(`#minimized-${modalState.id}`);
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create new indicator
        const indicator = document.createElement('div');
        indicator.id = `minimized-${modalState.id}`;
        indicator.className = 'minimized-modal-indicator';
        indicator.innerHTML = `
            <i class="fas fa-window-restore"></i>
            <span>${this.getModalTitle(modalState)}</span>
        `;

        // Add click to restore
        indicator.addEventListener('click', () => {
            this.minimizeModal(modalState); // This will restore it
        });

        // Add to taskbar area
        let taskbar = document.querySelector('.modal-taskbar');
        if (!taskbar) {
            taskbar = document.createElement('div');
            taskbar.className = 'modal-taskbar';
            document.body.appendChild(taskbar);
        }

        taskbar.appendChild(indicator);
    }

    toggleMaximize(modalState) {
        const { content } = modalState;

        if (modalState.isMaximized) {
            // Restore modal
            if (modalState.originalPosition) {
                content.style.left = modalState.originalPosition.left;
                content.style.top = modalState.originalPosition.top;
                content.style.width = modalState.originalPosition.width;
                content.style.height = modalState.originalPosition.height;
            }

            modalState.isMaximized = false;
            content.classList.remove('maximized');

            // Update maximize button
            const maximizeBtn = content.querySelector('.modal-maximize i');
            if (maximizeBtn) {
                maximizeBtn.className = 'fas fa-window-maximize';
            }
        } else {
            // Store original position and size
            modalState.originalPosition = {
                left: content.style.left || '50%',
                top: content.style.top || '50%',
                width: content.style.width || 'auto',
                height: content.style.height || 'auto'
            };

            // Maximize modal
            content.style.left = '0px';
            content.style.top = '0px';
            content.style.width = '100vw';
            content.style.height = '100vh';
            content.style.position = 'fixed';
            content.style.margin = '0';

            modalState.isMaximized = true;
            content.classList.add('maximized');

            // Update maximize button
            const maximizeBtn = content.querySelector('.modal-maximize i');
            if (maximizeBtn) {
                maximizeBtn.className = 'fas fa-window-restore';
            }
        }
    }

    closeModal(modalState) {
        const { element } = modalState;

        // Hide modal
        element.style.display = 'none';

        // Remove minimized indicator if exists
        const indicator = document.querySelector(`#minimized-${modalState.id}`);
        if (indicator) {
            indicator.remove();
        }

        // Reset modal state
        modalState.isMinimized = false;
        modalState.isMaximized = false;

        // Trigger any existing close handlers
        const closeEvent = new CustomEvent('modalClose', { detail: { modalId: modalState.id } });
        element.dispatchEvent(closeEvent);
    }

    getModalTitle(modalState) {
        const { content } = modalState;

        // Try to find title in various header elements
        const titleSelectors = [
            '.modal-header h3',
            '.modal-header h2',
            '.search-modal-header h3',
            '.note-editor-header h3',
            '.color-picker-header h3'
        ];

        for (const selector of titleSelectors) {
            const titleElement = content.querySelector(selector);
            if (titleElement) {
                return titleElement.textContent.trim();
            }
        }

        // Fallback to modal ID
        return modalState.id.replace('Modal', '');
    }

    centerModal(modalState) {
        const { content } = modalState;

        // Reset position to center
        content.style.position = 'fixed';
        content.style.left = '50%';
        content.style.top = '50%';
        content.style.transform = 'translate(-50%, -50%)';
        content.style.margin = '0';
    }

    addDraggableStyles() {
        // Check if styles already added
        if (document.querySelector('#draggable-modal-styles')) {
            return;
        }

        const styles = document.createElement('style');
        styles.id = 'draggable-modal-styles';
        styles.textContent = `
            /* Draggable Modal Styles */
            .modal-content, .search-modal-content, .note-editor-content, .color-picker-content {
                position: relative;
                transition: none !important;
            }

            .modal-content.dragging, .search-modal-content.dragging,
            .note-editor-content.dragging, .color-picker-content.dragging {
                cursor: move;
                user-select: none;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4) !important;
                transform: none !important;
            }

            .modal-content.resizing, .search-modal-content.resizing,
            .note-editor-content.resizing, .color-picker-content.resizing {
                user-select: none;
            }

            .modal-content.maximized, .search-modal-content.maximized,
            .note-editor-content.maximized, .color-picker-content.maximized {
                border-radius: 0 !important;
                max-width: none !important;
                max-height: none !important;
            }

            /* Resize Handles */
            .resize-handle {
                position: absolute;
                background: transparent;
                z-index: 10;
            }

            .resize-nw, .resize-ne, .resize-sw, .resize-se {
                width: 10px;
                height: 10px;
            }

            .resize-n, .resize-s {
                height: 5px;
                left: 10px;
                right: 10px;
            }

            .resize-e, .resize-w {
                width: 5px;
                top: 10px;
                bottom: 10px;
            }

            .resize-nw { top: -5px; left: -5px; cursor: nw-resize; }
            .resize-ne { top: -5px; right: -5px; cursor: ne-resize; }
            .resize-sw { bottom: -5px; left: -5px; cursor: sw-resize; }
            .resize-se { bottom: -5px; right: -5px; cursor: se-resize; }
            .resize-n { top: -2px; cursor: n-resize; }
            .resize-s { bottom: -2px; cursor: s-resize; }
            .resize-e { right: -2px; cursor: e-resize; }
            .resize-w { left: -2px; cursor: w-resize; }

            /* Window Controls */
            .modal-control-btn {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: var(--text-color);
                width: 30px;
                height: 30px;
                border-radius: 50%;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                margin-left: 5px;
                transition: all 0.3s ease;
                font-size: 12px;
            }

            .modal-control-btn:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.1);
            }

            .modal-minimize:hover {
                background: rgba(255, 193, 7, 0.8);
            }

            .modal-maximize:hover {
                background: rgba(40, 167, 69, 0.8);
            }

            /* Taskbar for minimized modals */
            .modal-taskbar {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                height: 50px;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(10px);
                display: flex;
                align-items: center;
                padding: 0 20px;
                gap: 10px;
                z-index: 9999;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .minimized-modal-indicator {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.3s ease;
                font-size: 14px;
            }

            .minimized-modal-indicator:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
            }

            /* Drag Handle */
            .modal-drag-handle {
                background: rgba(0, 0, 0, 0.1);
                padding: 5px 10px;
                text-align: center;
                font-size: 12px;
                color: var(--text-color);
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                margin-bottom: 10px;
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .resize-handle {
                    display: none;
                }

                .modal-content, .search-modal-content,
                .note-editor-content, .color-picker-content {
                    max-width: 95vw !important;
                    max-height: 95vh !important;
                }

                .modal-taskbar {
                    height: 60px;
                    padding: 0 10px;
                }

                .minimized-modal-indicator {
                    padding: 10px;
                    font-size: 16px;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    // Public API methods
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';

            if (this.modals.has(modalId)) {
                const modalState = this.modals.get(modalId);
                this.bringToFront(modalState);

                // Center modal if not positioned
                if (!modalState.content.style.left && !modalState.content.style.top) {
                    this.centerModal(modalState);
                }
            }
        }
    }

    closeModalById(modalId) {
        if (this.modals.has(modalId)) {
            this.closeModal(this.modals.get(modalId));
        }
    }

    minimizeModalById(modalId) {
        if (this.modals.has(modalId)) {
            this.minimizeModal(this.modals.get(modalId));
        }
    }

    maximizeModalById(modalId) {
        if (this.modals.has(modalId)) {
            this.toggleMaximize(this.modals.get(modalId));
        }
    }

    centerModalById(modalId) {
        if (this.modals.has(modalId)) {
            this.centerModal(this.modals.get(modalId));
        }
    }
}

// Initialize the system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.draggableModalSystem = new DraggableModalSystem();
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.draggableModalSystem = new DraggableModalSystem();
    });
} else {
    window.draggableModalSystem = new DraggableModalSystem();
}
